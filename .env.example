# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=dev-secret-key
JWT_SECRET_KEY=your-jwt-secret-key-here

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Development Environment Variables
DEV_JWT_KEY=your-dev-jwt-key-here # Could be the same value as JWT_SECRET_KEY
DEV_OPENAI_KEY=your-dev-openai-api-key-here

# Production Environment Variables (for production deployment)
PROD_JWT_KEY=your-prod-jwt-key-here # Could be the same value as JWT_SECRET_KEY
PROD_OPENAI_KEY=your-prod-openai-api-key-here

# Database Configuration (if needed in future)
# DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# Document Processing Configuration
UPLOAD_FOLDER=uploads/
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Vector Database Configuration
# For Docker Compose Qdrant service (recommended)
VECTOR_DB_URL=http://localhost:6333
VECTOR_DB_COLLECTION=health_documents

# For local development without Docker
# VECTOR_DB_LOCAL_PATH=./qdrant_data

# For remote Qdrant (optional)
# VECTOR_DB_URL=https://your-qdrant-instance.com
# VECTOR_DB_API_KEY=your-qdrant-api-key

# Medical Embedding Models
MEDICAL_TEXT_EMBEDDING_MODEL=emilyalsentzer/Bio_ClinicalBERT
MEDICAL_IMAGE_EMBEDDING_MODEL=microsoft/BiomedVLP-CXR-BERT-specialized

# Additional Configuration
PORT=5000
REACT_APP_API_URL=http://localhost:5000
