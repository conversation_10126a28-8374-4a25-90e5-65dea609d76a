# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/

# Documentation
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Logs
*.log

# Docker
Dockerfile
.dockerignore
