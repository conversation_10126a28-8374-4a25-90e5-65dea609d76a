#!/usr/bin/env python3
"""
Batch Medical Image Classification Validation

This script processes multiple medical images without interactive prompts,
useful for large datasets or automated testing.
"""

import os
import sys
import json
import datetime
from pathlib import Path
from typing import Dict, List, Any
import csv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.medical_image_classifier import MedicalImageClassifier
from utils.document_processor import DocumentProcessor

class BatchMedicalValidator:
    """Batch medical image classification validator."""
    
    def __init__(self, test_images_dir: str = "test_medical_images", results_dir: str = "validation_results"):
        """Initialize the batch validator."""
        self.test_images_dir = Path(test_images_dir)
        self.results_dir = Path(results_dir)
        
        # Create directories if they don't exist
        self.test_images_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)
        
        # Initialize components
        self.classifier = MedicalImageClassifier()
        self.document_processor = DocumentProcessor()
        
        # Results storage
        self.batch_results = []
        self.session_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Supported image extensions
        self.supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', 
                                   '.dcm', '.dicom', '.ima', '.img'}
        
        print(f"🏥 Batch Medical Image Validator Initialized")
        print(f"📁 Test images directory: {self.test_images_dir.absolute()}")
        print(f"📊 Results directory: {self.results_dir.absolute()}")
        print(f"🆔 Session ID: {self.session_id}")
    
    def get_test_images(self) -> List[Path]:
        """Get list of test image files."""
        image_files = []
        for ext in self.supported_extensions:
            image_files.extend(self.test_images_dir.rglob(f"*{ext}"))
        return sorted(image_files)
    
    def analyze_image_batch(self, image_path: Path) -> Dict[str, Any]:
        """Analyze a single medical image for batch processing."""
        try:
            # Read image file
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            # Get file info
            file_info = {
                'filename': image_path.name,
                'file_size': len(image_bytes),
                'file_path': str(image_path.relative_to(self.test_images_dir)),
                'file_extension': image_path.suffix.lower(),
                'subdirectory': image_path.parent.name if image_path.parent != self.test_images_dir else 'root'
            }
            
            # Classify with enhanced medical classifier
            classification_result = self.classifier.analyze_medical_image(image_bytes, image_path.name)
            
            # Generate medical description
            medical_description = self.classifier.create_medical_description(image_path.name, classification_result)
            
            # Process with document processor
            doc_chunks = self.document_processor._process_image_bytes(image_bytes, image_path.name)
            
            # Prepare results
            analysis_result = {
                'file_info': file_info,
                'classification': classification_result,
                'medical_description': medical_description,
                'document_chunks_count': len(doc_chunks),
                'processing_timestamp': datetime.datetime.now().isoformat(),
                'processing_success': True
            }
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ Error analyzing {image_path.name}: {e}")
            return {
                'file_info': {
                    'filename': image_path.name,
                    'file_path': str(image_path.relative_to(self.test_images_dir)),
                    'error': str(e)
                },
                'classification': {'analysis_error': str(e)},
                'medical_description': f"Error analyzing {image_path.name}",
                'processing_timestamp': datetime.datetime.now().isoformat(),
                'processing_success': False
            }
    
    def run_batch_validation(self):
        """Run batch validation on all test images."""
        print("\n" + "="*80)
        print("🚀 STARTING BATCH MEDICAL IMAGE VALIDATION")
        print("="*80)
        
        # Get test images
        test_images = self.get_test_images()
        if not test_images:
            print("❌ No test images found. Please add medical images to the test directory.")
            return
        
        print(f"\n📋 Found {len(test_images)} test images to process")
        
        # Process each image
        successful_analyses = 0
        failed_analyses = 0
        
        for i, image_path in enumerate(test_images, 1):
            print(f"🔍 Processing {i}/{len(test_images)}: {image_path.name}")
            
            # Analyze image
            result = self.analyze_image_batch(image_path)
            self.batch_results.append(result)
            
            if result.get('processing_success', False):
                successful_analyses += 1
                # Print brief summary
                classification = result.get('classification', {})
                medical_type = classification.get('medical_type', 'unknown')
                is_dicom = classification.get('is_dicom', False)
                print(f"   ✅ {medical_type} {'(DICOM)' if is_dicom else ''}")
            else:
                failed_analyses += 1
                print(f"   ❌ Analysis failed")
        
        print(f"\n📊 Batch processing completed:")
        print(f"   ✅ Successful: {successful_analyses}")
        print(f"   ❌ Failed: {failed_analyses}")
        print(f"   📈 Success rate: {(successful_analyses/len(test_images)*100):.1f}%")
        
        # Generate reports
        self.generate_batch_reports()
    
    def generate_batch_reports(self):
        """Generate comprehensive batch validation reports."""
        if not self.batch_results:
            print("⚠️  No batch results to report")
            return
        
        # File paths
        detailed_json = self.results_dir / f"{self.session_id}_batch_detailed.json"
        summary_json = self.results_dir / f"{self.session_id}_batch_summary.json"
        csv_report = self.results_dir / f"{self.session_id}_batch_results.csv"
        text_summary = self.results_dir / f"{self.session_id}_batch_summary.txt"
        
        # Calculate statistics
        total_images = len(self.batch_results)
        successful = sum(1 for r in self.batch_results if r.get('processing_success', False))
        failed = total_images - successful
        
        # Medical type distribution
        medical_types = {}
        dicom_count = 0
        subdirectory_stats = {}
        file_extension_stats = {}
        
        for result in self.batch_results:
            if result.get('processing_success', False):
                # Medical type distribution
                med_type = result['classification'].get('medical_type', 'unknown')
                medical_types[med_type] = medical_types.get(med_type, 0) + 1
                
                # DICOM count
                if result['classification'].get('is_dicom', False):
                    dicom_count += 1
                
                # Subdirectory stats
                subdir = result['file_info'].get('subdirectory', 'unknown')
                subdirectory_stats[subdir] = subdirectory_stats.get(subdir, 0) + 1
                
                # File extension stats
                ext = result['file_info'].get('file_extension', 'unknown')
                file_extension_stats[ext] = file_extension_stats.get(ext, 0) + 1
        
        # Generate summary statistics
        summary_stats = {
            'session_info': {
                'session_id': self.session_id,
                'generated_at': datetime.datetime.now().isoformat(),
                'total_images': total_images,
                'successful_analyses': successful,
                'failed_analyses': failed,
                'success_rate': (successful / total_images) if total_images > 0 else 0
            },
            'medical_type_distribution': medical_types,
            'dicom_detection': {
                'dicom_files_detected': dicom_count,
                'dicom_percentage': (dicom_count / successful * 100) if successful > 0 else 0
            },
            'subdirectory_distribution': subdirectory_stats,
            'file_extension_distribution': file_extension_stats
        }
        
        # Save detailed JSON report
        detailed_report = {
            'summary_statistics': summary_stats,
            'detailed_results': self.batch_results
        }
        
        with open(detailed_json, 'w') as f:
            json.dump(detailed_report, f, indent=2)
        
        # Save summary JSON
        with open(summary_json, 'w') as f:
            json.dump(summary_stats, f, indent=2)
        
        # Generate CSV report
        with open(csv_report, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # CSV headers
            headers = [
                'filename', 'file_path', 'subdirectory', 'file_extension', 'file_size',
                'medical_type', 'is_dicom', 'dicom_modality', 'body_part_examined',
                'width', 'height', 'is_grayscale', 'aspect_ratio',
                'medical_relevance_score', 'filename_indicators',
                'processing_success', 'analysis_error'
            ]
            writer.writerow(headers)
            
            # CSV data
            for result in self.batch_results:
                file_info = result.get('file_info', {})
                classification = result.get('classification', {})
                medical_context = classification.get('medical_context', {})
                
                row = [
                    file_info.get('filename', ''),
                    file_info.get('file_path', ''),
                    file_info.get('subdirectory', ''),
                    file_info.get('file_extension', ''),
                    file_info.get('file_size', ''),
                    classification.get('medical_type', ''),
                    classification.get('is_dicom', False),
                    classification.get('modality', ''),
                    classification.get('body_part_examined', ''),
                    classification.get('width', ''),
                    classification.get('height', ''),
                    classification.get('is_grayscale', ''),
                    classification.get('aspect_ratio', ''),
                    medical_context.get('medical_relevance_score', ''),
                    '; '.join(medical_context.get('filename_indicators', [])),
                    result.get('processing_success', False),
                    classification.get('analysis_error', '')
                ]
                writer.writerow(row)
        
        # Generate text summary
        text_content = f"""
BATCH MEDICAL IMAGE CLASSIFICATION VALIDATION REPORT
Session ID: {self.session_id}
Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

PROCESSING STATISTICS:
- Total Images: {total_images}
- Successful Analyses: {successful}
- Failed Analyses: {failed}
- Success Rate: {(successful/total_images*100):.1f}%

MEDICAL TYPE DISTRIBUTION:
"""
        for med_type, count in sorted(medical_types.items()):
            percentage = (count / successful * 100) if successful > 0 else 0
            text_content += f"- {med_type}: {count} images ({percentage:.1f}%)\n"
        
        text_content += f"\nDICOM DETECTION:\n"
        text_content += f"- DICOM files detected: {dicom_count}\n"
        text_content += f"- DICOM percentage: {(dicom_count/successful*100) if successful > 0 else 0:.1f}%\n"
        
        text_content += f"\nSUBDIRECTORY DISTRIBUTION:\n"
        for subdir, count in sorted(subdirectory_stats.items()):
            text_content += f"- {subdir}: {count} images\n"
        
        text_content += f"\nFILE FORMAT DISTRIBUTION:\n"
        for ext, count in sorted(file_extension_stats.items()):
            text_content += f"- {ext}: {count} images\n"
        
        with open(text_summary, 'w') as f:
            f.write(text_content)
        
        print(f"\n📊 BATCH VALIDATION REPORTS GENERATED:")
        print(f"   📋 Summary: {text_summary}")
        print(f"   📊 CSV Report: {csv_report}")
        print(f"   📄 JSON Summary: {summary_json}")
        print(f"   📁 Detailed JSON: {detailed_json}")
        print(text_content)

def main():
    """Main batch validation function."""
    print("🏥 BATCH MEDICAL IMAGE CLASSIFICATION VALIDATION")
    print("="*80)
    
    # Initialize validator
    validator = BatchMedicalValidator()
    
    # Check for test images
    test_images = validator.get_test_images()
    if not test_images:
        print("\n❌ No test images found.")
        print("Please add medical images to the test_medical_images/ directory")
        print("Run: python setup_validation_environment.py to create the directory structure")
        return
    
    print(f"\n📋 Found {len(test_images)} test images")
    
    # Confirm start
    start_validation = input(f"\n🚀 Start batch validation? (y/n): ").lower().strip()
    if start_validation not in ['y', 'yes']:
        print("Batch validation cancelled.")
        return
    
    # Run validation
    try:
        validator.run_batch_validation()
        print(f"\n✅ Batch validation completed successfully!")
        print(f"📁 Results saved in: {validator.results_dir.absolute()}")
    except Exception as e:
        print(f"\n❌ Batch validation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
