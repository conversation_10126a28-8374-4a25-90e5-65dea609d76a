#!/usr/bin/env python3
"""
Create sample medical images for testing the validation framework.
"""

import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_sample_images():
    """Create sample medical images for validation testing."""
    
    test_dir = Path("test_medical_images")
    
    # Create sample chest X-ray
    chest_xray_dir = test_dir / "chest_xrays"
    chest_xray_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a grayscale chest X-ray simulation
    width, height = 1200, 800
    image = Image.new('L', (width, height), color=128)
    pixels = np.array(image)
    
    # Add lung areas (darker)
    pixels[200:600, 300:500] = 80
    pixels[200:600, 700:900] = 80
    
    # Add heart area (brighter)
    pixels[300:500, 550:650] = 180
    
    # Add ribs (slightly brighter lines)
    for i in range(5):
        y = 250 + i * 80
        pixels[y:y+3, 200:1000] = 150
    
    image = Image.fromarray(pixels)
    image.save(chest_xray_dir / "chest_xray_pa.jpg", "JPEG")
    
    # Create sample CT scan
    ct_dir = test_dir / "ct_scans"
    ct_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a square grayscale CT scan simulation
    width, height = 512, 512
    image = Image.new('L', (width, height), color=100)
    pixels = np.array(image)
    
    # Add circular brain outline
    center_x, center_y = width // 2, height // 2
    y, x = np.ogrid[:height, :width]
    mask = (x - center_x)**2 + (y - center_y)**2 < (width//3)**2
    pixels[mask] = 150
    
    # Add brain structures
    inner_mask = (x - center_x)**2 + (y - center_y)**2 < (width//6)**2
    pixels[inner_mask] = 120
    
    image = Image.fromarray(pixels)
    image.save(ct_dir / "ct_brain_axial.jpg", "JPEG")
    
    # Create sample MRI
    mri_dir = test_dir / "mri_images"
    mri_dir.mkdir(parents=True, exist_ok=True)
    
    # Create an MRI simulation
    width, height = 256, 256
    image = Image.new('L', (width, height), color=50)
    pixels = np.array(image)
    
    # Add brain tissue patterns
    center_x, center_y = width // 2, height // 2
    y, x = np.ogrid[:height, :width]
    
    # White matter
    mask1 = (x - center_x)**2 + (y - center_y)**2 < (width//4)**2
    pixels[mask1] = 200
    
    # Gray matter
    mask2 = (x - center_x)**2 + (y - center_y)**2 < (width//3)**2
    mask2 = mask2 & ~mask1
    pixels[mask2] = 150
    
    image = Image.fromarray(pixels)
    image.save(mri_dir / "mri_brain_t1.jpg", "JPEG")
    
    # Create sample ultrasound
    us_dir = test_dir / "ultrasounds"
    us_dir.mkdir(parents=True, exist_ok=True)
    
    # Create an ultrasound simulation
    width, height = 640, 480
    image = Image.new('L', (width, height), color=30)
    pixels = np.array(image)
    
    # Add ultrasound-like speckle pattern
    noise = np.random.normal(0, 20, (height, width))
    pixels = np.clip(pixels + noise, 0, 255).astype(np.uint8)
    
    # Add some anatomical structures
    pixels[200:300, 250:400] = 120
    pixels[150:180, 200:450] = 80
    
    image = Image.fromarray(pixels)
    image.save(us_dir / "ultrasound_abdomen.jpg", "JPEG")
    
    # Create sample clinical photo
    clinical_dir = test_dir / "clinical_photos"
    clinical_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a color clinical photo simulation
    width, height = 800, 600
    image = Image.new('RGB', (width, height), color=(220, 180, 160))
    pixels = np.array(image)
    
    # Add skin-like color variation
    pixels[100:500, 200:600] = [200, 160, 140]
    pixels[200:300, 300:400] = [180, 140, 120]  # Lesion area
    
    image = Image.fromarray(pixels)
    image.save(clinical_dir / "skin_lesion.jpg", "JPEG")
    
    # Create sample pathology image
    pathology_dir = test_dir / "pathology_images"
    pathology_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a microscopy simulation
    width, height = 1024, 768
    image = Image.new('RGB', (width, height), color=(240, 220, 240))
    draw = ImageDraw.Draw(image)
    
    # Add cell-like structures
    for i in range(50):
        x = np.random.randint(50, width-50)
        y = np.random.randint(50, height-50)
        radius = np.random.randint(10, 30)
        color = (
            np.random.randint(150, 255),
            np.random.randint(100, 200),
            np.random.randint(150, 255)
        )
        draw.ellipse([x-radius, y-radius, x+radius, y+radius], fill=color)
    
    image.save(pathology_dir / "h_e_stain.jpg", "JPEG")
    
    # Create a generic medical image with descriptive filename
    other_dir = test_dir / "other_medical"
    other_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a generic medical image
    width, height = 600, 600
    image = Image.new('L', (width, height), color=120)
    pixels = np.array(image)
    
    # Add some pattern
    for i in range(0, width, 50):
        pixels[i:i+2, :] = 160
        pixels[:, i:i+2] = 160
    
    image = Image.fromarray(pixels)
    image.save(other_dir / "medical_scan_unknown.jpg", "JPEG")
    
    print("✅ Sample medical images created successfully!")
    print("\nCreated images:")
    print("📁 chest_xrays/chest_xray_pa.jpg")
    print("📁 ct_scans/ct_brain_axial.jpg") 
    print("📁 mri_images/mri_brain_t1.jpg")
    print("📁 ultrasounds/ultrasound_abdomen.jpg")
    print("📁 clinical_photos/skin_lesion.jpg")
    print("📁 pathology_images/h_e_stain.jpg")
    print("📁 other_medical/medical_scan_unknown.jpg")
    print("\n🚀 Ready for validation testing!")
    print("Run: python simple_medical_validation.py")

if __name__ == "__main__":
    create_sample_images()
