#!/usr/bin/env python3
"""
Setup script for medical image validation environment.
Creates organized directory structure for test images.
"""

import os
from pathlib import Path
import shutil

def setup_validation_directories():
    """Create organized directory structure for medical image validation."""

    base_dir = Path("test_medical_images")
    results_dir = Path("validation_results")

    # Create main directories
    base_dir.mkdir(exist_ok=True)
    results_dir.mkdir(exist_ok=True)

    # Create organized subdirectories for different medical image types
    subdirs = [
        "chest_xrays",
        "ct_scans",
        "mri_images",
        "ultrasounds",
        "dicom_files",
        "mammography",
        "clinical_photos",
        "pathology_images",
        "retinal_images",
        "dermatology_images",
        "other_medical"
    ]

    for subdir in subdirs:
        (base_dir / subdir).mkdir(exist_ok=True)

    # Create README file with instructions
    readme_content = """# Medical Image Validation Test Directory

This directory is organized to help you systematically test the enhanced medical image classification system.

## Directory Structure:

### 📁 chest_xrays/
- Place chest X-ray images here
- Supported formats: .jpg, .jpeg, .png, .dcm, .dicom
- Examples: chest_pa.jpg, chest_lateral.dcm

### 📁 ct_scans/
- Place CT scan images here
- Both DICOM and standard image formats supported
- Examples: ct_head.dcm, ct_abdomen.jpg

### 📁 mri_images/
- Place MRI images here
- T1, T2, FLAIR, DWI sequences welcome
- Examples: mri_brain_t1.dcm, mri_spine.jpg

### 📁 ultrasounds/
- Place ultrasound images here
- Abdominal, cardiac, obstetric ultrasounds
- Examples: us_abdomen.jpg, echo_heart.dcm

### 📁 dicom_files/
- Place any DICOM files here for DICOM-specific testing
- Will test DICOM metadata extraction
- Examples: any .dcm or .dicom files

### 📁 mammography/
- Place mammography images here
- Digital mammograms, tomosynthesis
- Examples: mammo_cc.dcm, mammo_mlo.jpg

### 📁 clinical_photos/
- Place clinical photographs here
- Wound photos, skin lesions, etc.
- Examples: wound_day1.jpg, lesion_dermoscopy.png

### 📁 pathology_images/
- Place histology/pathology images here
- Microscopy images, tissue samples
- Examples: h_e_slide.jpg, immunostain.tiff

### 📁 retinal_images/
- Place ophthalmology images here
- Fundus photos, OCT images
- Examples: fundus_od.jpg, oct_macula.png

### 📁 dermatology_images/
- Place dermatological images here
- Skin lesions, rashes, etc.
- Examples: melanoma.jpg, rash_arm.png

### 📁 other_medical/
- Place any other medical images here
- Endoscopy, nuclear medicine, etc.
- Examples: endoscopy_colon.jpg, pet_scan.dcm

## Supported File Formats:
- Standard images: .jpg, .jpeg, .png, .bmp, .tiff, .tif
- DICOM files: .dcm, .dicom, .ima, .img

## Naming Conventions (Optional but Helpful):
- Include medical terms in filenames for better testing
- Examples:
  - chest_xray_pa.jpg
  - ct_brain_axial.dcm
  - mri_knee_t2.jpg
  - us_gallbladder.png

## Running Validation:
1. Add your medical images to the appropriate subdirectories
2. Run: python medical_image_validation.py
3. Follow the interactive prompts to validate classifications
4. Review results in the validation_results/ directory

## Tips:
- Start with a small set of images (5-10) to test the workflow
- Include both DICOM and standard image formats
- Mix different medical image types for comprehensive testing
- Use clear, descriptive filenames when possible
"""

    with open(base_dir / "README.md", "w", encoding='utf-8') as f:
        f.write(readme_content)

    print("🏥 Medical Image Validation Environment Setup Complete!")
    print("="*60)
    print(f"📁 Test images directory: {base_dir.absolute()}")
    print(f"📊 Results directory: {results_dir.absolute()}")
    print("\n📋 Created subdirectories:")
    for subdir in subdirs:
        print(f"   - {subdir}/")

    print(f"\n📖 Instructions saved to: {base_dir / 'README.md'}")
    print("\n🚀 Next steps:")
    print("1. Add your medical image samples to the appropriate subdirectories")
    print("2. Run: python medical_image_validation.py")
    print("3. Follow the interactive validation process")

def copy_sample_images():
    """Copy any existing sample images to the test directory."""
    # This function can be used to copy sample images if they exist
    pass

if __name__ == "__main__":
    setup_validation_directories()
