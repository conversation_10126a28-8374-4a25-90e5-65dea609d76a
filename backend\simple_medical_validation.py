#!/usr/bin/env python3
"""
Simplified Medical Image Classification Validation

This script provides a lightweight validation framework that doesn't depend
on the full service stack, making it easier to run standalone validation.
"""

import os
import sys
import json
import datetime
from pathlib import Path
from typing import Dict, List, Any
from PIL import Image
import io

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.medical_image_classifier import MedicalImageClassifier

class SimpleMedicalValidator:
    """Simplified medical image classification validator."""
    
    def __init__(self, test_images_dir: str = "test_medical_images", results_dir: str = "validation_results"):
        """Initialize the simplified validator."""
        self.test_images_dir = Path(test_images_dir)
        self.results_dir = Path(results_dir)
        
        # Create directories if they don't exist
        self.test_images_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)
        
        # Initialize only the medical image classifier
        print("🔧 Initializing Medical Image Classifier...")
        self.classifier = MedicalImageClassifier()
        
        # Results storage
        self.validation_results = []
        self.session_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Supported image extensions
        self.supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', 
                                   '.dcm', '.dicom', '.ima', '.img'}
        
        print(f"🏥 Simple Medical Image Validator Initialized")
        print(f"📁 Test images directory: {self.test_images_dir.absolute()}")
        print(f"📊 Results directory: {self.results_dir.absolute()}")
        print(f"🆔 Session ID: {self.session_id}")
    
    def get_test_images(self) -> List[Path]:
        """Get list of test image files."""
        image_files = []
        for ext in self.supported_extensions:
            image_files.extend(self.test_images_dir.rglob(f"*{ext}"))
        return sorted(image_files)
    
    def analyze_single_image(self, image_path: Path) -> Dict[str, Any]:
        """Analyze a single medical image."""
        print(f"\n🔍 Analyzing: {image_path.name}")
        
        try:
            # Read image file
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            # Get file info
            file_info = {
                'filename': image_path.name,
                'file_size': len(image_bytes),
                'file_path': str(image_path.relative_to(self.test_images_dir)),
                'file_extension': image_path.suffix.lower(),
                'subdirectory': image_path.parent.name if image_path.parent != self.test_images_dir else 'root'
            }
            
            # Classify with enhanced medical classifier
            classification_result = self.classifier.analyze_medical_image(image_bytes, image_path.name)
            
            # Generate medical description
            medical_description = self.classifier.create_medical_description(image_path.name, classification_result)
            
            # Prepare comprehensive results
            analysis_result = {
                'file_info': file_info,
                'classification': classification_result,
                'medical_description': medical_description,
                'timestamp': datetime.datetime.now().isoformat(),
                'processing_success': True
            }
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ Error analyzing {image_path.name}: {e}")
            return {
                'file_info': {'filename': image_path.name, 'error': str(e)},
                'classification': {'analysis_error': str(e)},
                'medical_description': f"Error analyzing {image_path.name}",
                'timestamp': datetime.datetime.now().isoformat(),
                'processing_success': False
            }
    
    def display_analysis_results(self, result: Dict[str, Any]):
        """Display analysis results in a clear format."""
        print("\n" + "="*60)
        print("📊 MEDICAL IMAGE ANALYSIS RESULTS")
        print("="*60)
        
        # File Information
        file_info = result.get('file_info', {})
        print(f"\n📁 FILE INFORMATION:")
        print(f"   Filename: {file_info.get('filename', 'Unknown')}")
        print(f"   File Path: {file_info.get('file_path', 'Unknown')}")
        print(f"   File Size: {file_info.get('file_size', 0):,} bytes")
        print(f"   Extension: {file_info.get('file_extension', 'Unknown')}")
        print(f"   Subdirectory: {file_info.get('subdirectory', 'Unknown')}")
        
        # Classification Results
        classification = result.get('classification', {})
        print(f"\n🏥 MEDICAL CLASSIFICATION:")
        print(f"   Medical Type: {classification.get('medical_type', 'Unknown')}")
        print(f"   Is DICOM: {classification.get('is_dicom', False)}")
        
        if classification.get('is_dicom', False):
            print(f"   DICOM Modality: {classification.get('modality', 'Unknown')}")
            print(f"   Body Part: {classification.get('body_part_examined', 'Unknown')}")
            print(f"   Study Description: {classification.get('study_description', 'N/A')}")
            print(f"   Series Description: {classification.get('series_description', 'N/A')}")
        
        # Image Properties
        if 'width' in classification and 'height' in classification:
            print(f"   Dimensions: {classification['width']}x{classification['height']}")
        print(f"   Is Grayscale: {classification.get('is_grayscale', 'Unknown')}")
        print(f"   Aspect Ratio: {classification.get('aspect_ratio', 'Unknown')}")
        
        # Medical Context
        medical_context = classification.get('medical_context', {})
        if medical_context:
            print(f"\n🎯 MEDICAL CONTEXT:")
            print(f"   Relevance Score: {medical_context.get('medical_relevance_score', 'Unknown')}")
            filename_indicators = medical_context.get('filename_indicators', [])
            if filename_indicators:
                print(f"   Filename Indicators: {', '.join(filename_indicators)}")
        
        # Medical Description
        description = result.get('medical_description', '')
        print(f"\n📝 MEDICAL DESCRIPTION:")
        print(f"   {description}")
        
        # Error Information
        if 'analysis_error' in classification:
            print(f"\n⚠️  ANALYSIS ERROR:")
            print(f"   {classification['analysis_error']}")
    
    def get_user_feedback(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Get user feedback on the classification results."""
        print(f"\n" + "="*60)
        print("💬 VALIDATION FEEDBACK")
        print("="*60)
        
        feedback = {}
        
        # Overall accuracy
        while True:
            accuracy = input("\n✅ Is the medical type classification CORRECT? (y/n/s to skip): ").lower().strip()
            if accuracy in ['y', 'yes', 'n', 'no', 's', 'skip']:
                feedback['classification_correct'] = accuracy in ['y', 'yes']
                feedback['skipped'] = accuracy in ['s', 'skip']
                break
            print("Please enter 'y' for yes, 'n' for no, or 's' to skip")
        
        if not feedback.get('skipped', False):
            # If incorrect, get correct classification
            if not feedback['classification_correct']:
                correct_type = input("🔧 What is the CORRECT medical image type? ").strip()
                feedback['correct_medical_type'] = correct_type
            
            # DICOM feedback
            is_dicom = result.get('classification', {}).get('is_dicom', False)
            dicom_feedback = input(f"📋 Is DICOM detection correct? (Currently: {is_dicom}) (y/n/s): ").lower().strip()
            if dicom_feedback in ['y', 'yes']:
                feedback['dicom_correct'] = True
            elif dicom_feedback in ['n', 'no']:
                feedback['dicom_correct'] = False
                feedback['correct_is_dicom'] = not is_dicom
            
            # Description quality
            desc_quality = input("📝 Rate the medical description quality (1-5, 5=excellent): ").strip()
            try:
                feedback['description_quality'] = int(desc_quality)
            except ValueError:
                feedback['description_quality'] = None
            
            # Additional comments
            comments = input("💭 Additional comments (optional): ").strip()
            if comments:
                feedback['comments'] = comments
        
        feedback['timestamp'] = datetime.datetime.now().isoformat()
        return feedback
    
    def save_validation_result(self, result: Dict[str, Any], feedback: Dict[str, Any]):
        """Save validation result with feedback."""
        validation_entry = {
            'session_id': self.session_id,
            'analysis_result': result,
            'user_feedback': feedback,
            'validation_timestamp': datetime.datetime.now().isoformat()
        }
        
        self.validation_results.append(validation_entry)
        
        # Save individual result file
        filename = result['file_info']['filename']
        safe_filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
        result_file = self.results_dir / f"{self.session_id}_{safe_filename}_result.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(validation_entry, f, indent=2)
    
    def run_validation(self):
        """Run the validation process."""
        print("\n" + "="*80)
        print("🚀 STARTING MEDICAL IMAGE VALIDATION")
        print("="*80)
        
        # Get test images
        test_images = self.get_test_images()
        if not test_images:
            print("❌ No test images found. Please add medical images to the test directory.")
            print("Run: python setup_validation_environment.py to create the directory structure")
            return
        
        print(f"\n📋 Found {len(test_images)} test images to validate")
        
        # Process each image
        for i, image_path in enumerate(test_images, 1):
            print(f"\n{'='*80}")
            print(f"🖼️  PROCESSING IMAGE {i}/{len(test_images)}")
            print(f"{'='*80}")
            
            # Analyze image
            result = self.analyze_single_image(image_path)
            
            # Display results
            self.display_analysis_results(result)
            
            # Get user feedback
            feedback = self.get_user_feedback(result)
            
            # Save result
            self.save_validation_result(result, feedback)
            
            # Check if user wants to continue
            if i < len(test_images):
                continue_validation = input(f"\n⏭️  Continue to next image? (y/n/q to quit): ").lower().strip()
                if continue_validation in ['n', 'no', 'q', 'quit']:
                    print(f"🛑 Validation stopped at user request ({i}/{len(test_images)} completed)")
                    break
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate validation summary."""
        if not self.validation_results:
            print("⚠️  No validation results to summarize")
            return
        
        total_images = len(self.validation_results)
        correct_classifications = sum(1 for r in self.validation_results 
                                    if r['user_feedback'].get('classification_correct', False))
        skipped = sum(1 for r in self.validation_results 
                     if r['user_feedback'].get('skipped', False))
        
        print(f"\n📊 VALIDATION SUMMARY:")
        print(f"   Total Images: {total_images}")
        print(f"   Correct Classifications: {correct_classifications}/{total_images - skipped}")
        if total_images > skipped:
            accuracy = (correct_classifications / (total_images - skipped)) * 100
            print(f"   Accuracy: {accuracy:.1f}%")
        print(f"   Skipped: {skipped}")
        
        summary_file = self.results_dir / f"{self.session_id}_summary.json"
        summary_data = {
            'session_id': self.session_id,
            'total_images': total_images,
            'correct_classifications': correct_classifications,
            'skipped': skipped,
            'accuracy': (correct_classifications / (total_images - skipped)) if total_images > skipped else 0,
            'results': self.validation_results
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2)
        
        print(f"📁 Results saved to: {summary_file}")

def main():
    """Main validation function."""
    print("🏥 SIMPLE MEDICAL IMAGE CLASSIFICATION VALIDATION")
    print("="*80)
    
    # Initialize validator
    try:
        validator = SimpleMedicalValidator()
    except Exception as e:
        print(f"❌ Failed to initialize validator: {e}")
        return
    
    # Check for test images
    test_images = validator.get_test_images()
    if not test_images:
        print("\n❌ No test images found.")
        print("Please add medical images to the test_medical_images/ directory")
        print("Run: python setup_validation_environment.py to create the directory structure")
        return
    
    print(f"\n📋 Found {len(test_images)} test images")
    
    # Confirm start
    start_validation = input(f"\n🚀 Start validation? (y/n): ").lower().strip()
    if start_validation not in ['y', 'yes']:
        print("Validation cancelled.")
        return
    
    # Run validation
    try:
        validator.run_validation()
        print(f"\n✅ Validation completed successfully!")
        print(f"📁 Results saved in: {validator.results_dir.absolute()}")
    except KeyboardInterrupt:
        print(f"\n⚠️  Validation interrupted by user")
        if validator.validation_results:
            validator.generate_summary()
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
