"""
Document processor utility.
This module contains functions for processing documents of various formats.
"""
import os
import re
import io
import numpy as np
from PIL import Image
from typing import List, Dict, Any, Tuple, Optional, Union
from pypdf import PdfReader
from docx import Document
import pandas as pd
from langchain.text_splitter import RecursiveCharacterTextSplitter

class DocumentProcessor:
    """Document processor class for handling various document formats."""

    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        Initialize the document processor.

        Args:
            chunk_size: The size of each chunk in characters.
            chunk_overlap: The overlap between chunks in characters.
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", ". ", " ", ""]
        )

    def process_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Process a file and return chunks with metadata.

        Args:
            file_path: The path to the file.

        Returns:
            A list of dictionaries containing chunks and metadata.
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        file_name = os.path.basename(file_path)

        if file_ext == '.pdf':
            return self._process_pdf(file_path, file_name)
        elif file_ext == '.txt':
            return self._process_text(file_path, file_name)
        elif file_ext in ['.docx', '.doc']:
            return self._process_docx(file_path, file_name)
        elif file_ext in ['.csv', '.xlsx', '.xls']:
            return self._process_tabular(file_path, file_name)
        elif file_ext in ['.png', '.jpg', '.jpeg']:
            return self._process_image(file_path, file_name)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")

    def process_bytes(self, file_bytes: bytes, file_name: str) -> List[Dict[str, Any]]:
        """
        Process file bytes and return chunks with metadata.

        Args:
            file_bytes: The file content as bytes.
            file_name: The name of the file.

        Returns:
            A list of dictionaries containing chunks and metadata.
        """
        file_ext = os.path.splitext(file_name)[1].lower()

        if file_ext == '.pdf':
            return self._process_pdf_bytes(file_bytes, file_name)
        elif file_ext == '.txt':
            return self._process_text_bytes(file_bytes, file_name)
        elif file_ext in ['.docx', '.doc']:
            return self._process_docx_bytes(file_bytes, file_name)
        elif file_ext in ['.csv', '.xlsx', '.xls']:
            return self._process_tabular_bytes(file_bytes, file_name)
        elif file_ext in ['.png', '.jpg', '.jpeg']:
            return self._process_image_bytes(file_bytes, file_name)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")

    def _process_pdf(self, file_path: str, file_name: str) -> List[Dict[str, Any]]:
        """Process a PDF file."""
        with open(file_path, 'rb') as f:
            return self._process_pdf_bytes(f.read(), file_name)

    def _process_pdf_bytes(self, file_bytes: bytes, file_name: str) -> List[Dict[str, Any]]:
        """Process PDF bytes."""
        chunks = []

        # Use pypdf for PDF processing
        try:
            from io import BytesIO
            from pypdf import PdfReader

            reader = PdfReader(BytesIO(file_bytes))

            for page_num, page in enumerate(reader.pages):
                text = page.extract_text()
                if not text.strip():
                    continue

                # Create chunks from text
                text_chunks = self.text_splitter.split_text(text)

                for i, chunk in enumerate(text_chunks):
                    chunks.append({
                        "content": chunk,
                        "metadata": {
                            "source": file_name,
                            "page": page_num + 1,
                            "chunk": i + 1,
                            "total_chunks": len(text_chunks),
                            "content_type": "text"
                        }
                    })

        except Exception as e:
            print(f"Error processing PDF with pypdf: {e}")

        return chunks

    def _extract_images_from_pdf_page(self, page) -> List[bytes]:
        """Extract images from a PDF page."""
        # We're not using PyMuPDF, so we can't extract images from PDF pages
        # This is a simplified version that returns an empty list
        return []

    def _process_text(self, file_path: str, file_name: str) -> List[Dict[str, Any]]:
        """Process a text file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            text = f.read()

        return self._process_text_content(text, file_name)

    def _process_text_bytes(self, file_bytes: bytes, file_name: str) -> List[Dict[str, Any]]:
        """Process text file bytes."""
        text = file_bytes.decode('utf-8')
        return self._process_text_content(text, file_name)

    def _process_text_content(self, text: str, file_name: str) -> List[Dict[str, Any]]:
        """Process text content."""
        chunks = []
        text_chunks = self.text_splitter.split_text(text)

        for i, chunk in enumerate(text_chunks):
            chunks.append({
                "content": chunk,
                "metadata": {
                    "source": file_name,
                    "chunk": i + 1,
                    "total_chunks": len(text_chunks),
                    "content_type": "text"
                }
            })

        return chunks

    def _process_docx(self, file_path: str, file_name: str) -> List[Dict[str, Any]]:
        """Process a DOCX file."""
        with open(file_path, 'rb') as f:
            return self._process_docx_bytes(f.read(), file_name)

    def _process_docx_bytes(self, file_bytes: bytes, file_name: str) -> List[Dict[str, Any]]:
        """Process DOCX bytes."""
        doc = Document(io.BytesIO(file_bytes))
        text = "\n".join([para.text for para in doc.paragraphs])
        return self._process_text_content(text, file_name)

    def _process_tabular(self, file_path: str, file_name: str) -> List[Dict[str, Any]]:
        """Process a tabular file (CSV, Excel)."""
        with open(file_path, 'rb') as f:
            return self._process_tabular_bytes(f.read(), file_name)

    def _process_tabular_bytes(self, file_bytes: bytes, file_name: str) -> List[Dict[str, Any]]:
        """Process tabular file bytes."""
        file_ext = os.path.splitext(file_name)[1].lower()

        if file_ext == '.csv':
            df = pd.read_csv(io.BytesIO(file_bytes))
        else:  # Excel files
            df = pd.read_excel(io.BytesIO(file_bytes))

        # Convert DataFrame to text
        text = df.to_string(index=False)
        return self._process_text_content(text, file_name)

    def _process_image(self, file_path: str, file_name: str) -> List[Dict[str, Any]]:
        """Process an image file."""
        with open(file_path, 'rb') as f:
            return self._process_image_bytes(f.read(), file_name)

    def _process_image_bytes(self, file_bytes: bytes, file_name: str) -> List[Dict[str, Any]]:
        """Process image bytes with enhanced medical context."""
        print(f"=== PROCESSING IMAGE BYTES ===")
        print(f"File name: {file_name}")
        print(f"File size: {len(file_bytes)} bytes")

        # Analyze image properties for better medical context
        image_info = self._analyze_medical_image(file_bytes, file_name)

        # Create enhanced content description
        content_description = self._create_medical_image_description(file_name, image_info)

        chunks = [{
            "content": content_description,
            "metadata": {
                "source": file_name,
                "content_type": "image",
                "image_data": file_bytes,
                "image_info": image_info,
                "medical_context": True
            }
        }]

        print(f"Created {len(chunks)} chunks for medical image")
        print(f"Chunk content: {chunks[0]['content']}")
        print(f"Image info: {image_info}")
        print(f"Chunk metadata keys: {list(chunks[0]['metadata'].keys())}")

        return chunks

    def _analyze_medical_image(self, file_bytes: bytes, file_name: str) -> Dict[str, Any]:
        """Analyze medical image properties."""
        try:
            from PIL import Image
            import io

            image = Image.open(io.BytesIO(file_bytes))

            # Extract basic image properties
            width, height = image.size
            mode = image.mode
            format_type = image.format or "Unknown"

            # Determine likely medical image type based on characteristics
            is_grayscale = mode in ['L', 'LA', '1']
            aspect_ratio = width / height

            # Infer medical image type
            medical_type = "medical_image"
            if is_grayscale:
                if 0.8 <= aspect_ratio <= 1.2:
                    medical_type = "radiological_scan"
                elif aspect_ratio > 1.2:
                    medical_type = "chest_xray"
                else:
                    medical_type = "medical_radiograph"
            else:
                if width > 1000 or height > 1000:
                    medical_type = "high_resolution_clinical_image"
                else:
                    medical_type = "clinical_photograph"

            return {
                "width": width,
                "height": height,
                "mode": mode,
                "format": format_type,
                "is_grayscale": is_grayscale,
                "aspect_ratio": round(aspect_ratio, 2),
                "medical_type": medical_type,
                "file_size_bytes": len(file_bytes)
            }

        except Exception as e:
            print(f"Error analyzing medical image: {e}")
            return {
                "medical_type": "medical_image",
                "file_size_bytes": len(file_bytes),
                "analysis_error": str(e)
            }

    def _create_medical_image_description(self, file_name: str, image_info: Dict[str, Any]) -> str:
        """Create a comprehensive medical image description for embedding."""

        # Base description
        medical_type = image_info.get("medical_type", "medical_image")

        # Create descriptive text based on image analysis
        description_parts = [
            f"Medical image: {file_name}",
            f"Type: {medical_type.replace('_', ' ')}"
        ]

        # Add technical details
        if "width" in image_info and "height" in image_info:
            description_parts.append(f"Dimensions: {image_info['width']}x{image_info['height']}")

        if image_info.get("is_grayscale"):
            description_parts.append("Grayscale radiological image")
        else:
            description_parts.append("Color clinical image")

        # Add medical context based on type
        medical_contexts = {
            "chest_xray": "chest radiograph for pulmonary and cardiac assessment",
            "radiological_scan": "medical scan for diagnostic evaluation",
            "medical_radiograph": "radiographic image for clinical diagnosis",
            "clinical_photograph": "clinical documentation photograph",
            "high_resolution_clinical_image": "detailed clinical imaging study"
        }

        medical_context = medical_contexts.get(medical_type, "medical image for healthcare documentation")
        description_parts.append(medical_context)

        # Add general medical keywords for better searchability
        description_parts.extend([
            "medical documentation",
            "clinical assessment",
            "diagnostic imaging",
            "healthcare record"
        ])

        return " | ".join(description_parts)
