"""
Enhanced medical image classification using DICOM standards and medical AI models.
"""

import io
import os
import logging
from typing import Dict, Any, Optional, Tuple, List
from PIL import Image
import numpy as np

# Set up logging
logger = logging.getLogger(__name__)


class MedicalImageClassifier:
    """
    Advanced medical image classifier that uses DICOM metadata and medical AI models
    for accurate medical image type identification and metadata extraction.
    """

    def __init__(self):
        """Initialize the medical image classifier."""
        self.dicom_available = self._check_dicom_availability()
        self.simpleitk_available = self._check_simpleitk_availability()
        self.medmnist_available = self._check_medmnist_availability()

        # DICOM modality mappings
        self.dicom_modalities = {
            'CR': 'computed_radiography',
            'CT': 'computed_tomography',
            'MR': 'magnetic_resonance',
            'NM': 'nuclear_medicine',
            'US': 'ultrasound',
            'XA': 'x_ray_angiography',
            'RF': 'radiofluoroscopy',
            'DX': 'digital_radiography',
            'MG': 'mammography',
            'IO': 'intra_oral_radiography',
            'PX': 'panoramic_x_ray',
            'GM': 'general_microscopy',
            'SM': 'slide_microscopy',
            'OT': 'other',
            'PT': 'positron_emission_tomography',
            'ES': 'endoscopy',
            'OP': 'ophthalmic_photography',
            'OPM': 'ophthalmic_mapping',
            'OPT': 'ophthalmic_tomography',
            'IVOCT': 'intravascular_optical_coherence_tomography',
            'IVUS': 'intravascular_ultrasound'
        }

        # Medical image file extensions that might contain DICOM data
        self.medical_extensions = {'.dcm', '.dicom', '.ima', '.img'}

        logger.info(f"MedicalImageClassifier initialized - DICOM: {self.dicom_available}, "
                   f"SimpleITK: {self.simpleitk_available}, MedMNIST: {self.medmnist_available}")

    def _check_dicom_availability(self) -> bool:
        """Check if pydicom is available."""
        try:
            import pydicom
            return True
        except ImportError:
            logger.warning("pydicom not available - DICOM analysis will be limited")
            return False

    def _check_simpleitk_availability(self) -> bool:
        """Check if SimpleITK is available."""
        try:
            import SimpleITK as sitk
            return True
        except ImportError:
            logger.warning("SimpleITK not available - advanced medical image analysis will be limited")
            return False

    def _check_medmnist_availability(self) -> bool:
        """Check if MedMNIST is available."""
        try:
            import medmnist
            return True
        except ImportError:
            logger.warning("MedMNIST not available - medical image classification models will be limited")
            return False

    def analyze_medical_image(self, file_bytes: bytes, file_name: str) -> Dict[str, Any]:
        """
        Analyze medical image using DICOM metadata and medical AI models.

        Args:
            file_bytes: Raw image file bytes
            file_name: Name of the image file

        Returns:
            Dictionary containing comprehensive medical image analysis
        """
        try:
            # First, try DICOM analysis if the file might be DICOM
            if self._might_be_dicom(file_bytes, file_name):
                dicom_result = self._analyze_dicom_image(file_bytes, file_name)
                if dicom_result.get('is_dicom', False):
                    return dicom_result

            # Fall back to standard image analysis with medical context
            return self._analyze_standard_medical_image(file_bytes, file_name)

        except Exception as e:
            logger.error(f"Error analyzing medical image {file_name}: {e}")
            return self._create_fallback_analysis(file_bytes, file_name, str(e))

    def _might_be_dicom(self, file_bytes: bytes, file_name: str) -> bool:
        """Check if file might be a DICOM file."""
        # Check file extension
        file_ext = os.path.splitext(file_name)[1].lower()
        if file_ext in self.medical_extensions:
            return True

        # Check DICOM magic bytes
        if len(file_bytes) > 132:
            # DICOM files have "DICM" at offset 128
            return file_bytes[128:132] == b'DICM'

        return False

    def _analyze_dicom_image(self, file_bytes: bytes, file_name: str) -> Dict[str, Any]:
        """Analyze DICOM image and extract medical metadata."""
        if not self.dicom_available:
            return self._analyze_standard_medical_image(file_bytes, file_name)

        try:
            import pydicom

            # Read DICOM file
            dicom_data = pydicom.dcmread(io.BytesIO(file_bytes), force=True)

            # Extract DICOM metadata
            modality = getattr(dicom_data, 'Modality', 'Unknown')
            body_part = getattr(dicom_data, 'BodyPartExamined', 'Unknown')
            study_description = getattr(dicom_data, 'StudyDescription', '')
            series_description = getattr(dicom_data, 'SeriesDescription', '')
            image_type = getattr(dicom_data, 'ImageType', [])
            photometric_interpretation = getattr(dicom_data, 'PhotometricInterpretation', 'Unknown')

            # Get image dimensions
            rows = getattr(dicom_data, 'Rows', 0)
            columns = getattr(dicom_data, 'Columns', 0)

            # Determine medical image type from DICOM modality
            medical_type = self.dicom_modalities.get(modality, 'medical_image')

            # Create comprehensive analysis
            analysis = {
                'is_dicom': True,
                'medical_type': medical_type,
                'modality': modality,
                'body_part_examined': body_part,
                'study_description': study_description,
                'series_description': series_description,
                'image_type': list(image_type) if image_type else [],
                'photometric_interpretation': photometric_interpretation,
                'width': columns,
                'height': rows,
                'file_size_bytes': len(file_bytes),
                'dicom_metadata': {
                    'patient_id': getattr(dicom_data, 'PatientID', ''),
                    'study_date': getattr(dicom_data, 'StudyDate', ''),
                    'acquisition_date': getattr(dicom_data, 'AcquisitionDate', ''),
                    'institution_name': getattr(dicom_data, 'InstitutionName', ''),
                    'manufacturer': getattr(dicom_data, 'Manufacturer', ''),
                    'manufacturer_model': getattr(dicom_data, 'ManufacturerModelName', ''),
                }
            }

            logger.info(f"Successfully analyzed DICOM image: {file_name} - Modality: {modality}")
            return analysis

        except Exception as e:
            logger.warning(f"Failed to analyze as DICOM: {e}")
            return self._analyze_standard_medical_image(file_bytes, file_name)

    def _analyze_standard_medical_image(self, file_bytes: bytes, file_name: str) -> Dict[str, Any]:
        """Analyze standard medical image using enhanced heuristics and AI models."""
        try:
            # Basic image analysis using PIL
            image = Image.open(io.BytesIO(file_bytes))
            width, height = image.size
            mode = image.mode
            format_type = image.format or "Unknown"

            # Enhanced medical image classification
            medical_type = self._classify_medical_image_type(image, file_name)

            # Extract additional medical context
            medical_context = self._extract_medical_context(image, file_name, medical_type)

            analysis = {
                'is_dicom': False,
                'medical_type': medical_type,
                'width': width,
                'height': height,
                'mode': mode,
                'format': format_type,
                'is_grayscale': mode in ['L', 'LA', '1'],
                'aspect_ratio': round(width / height, 2),
                'file_size_bytes': len(file_bytes),
                'medical_context': medical_context
            }

            logger.info(f"Successfully analyzed standard medical image: {file_name} - Type: {medical_type}")
            return analysis

        except Exception as e:
            logger.error(f"Failed to analyze standard medical image: {e}")
            return self._create_fallback_analysis(file_bytes, file_name, str(e))

    def _classify_medical_image_type(self, image: Image.Image, file_name: str) -> str:
        """
        Classify medical image type using enhanced heuristics and AI models.

        Args:
            image: PIL Image object
            file_name: Name of the image file

        Returns:
            Medical image type classification
        """
        try:
            # Use MedMNIST models if available
            if self.medmnist_available:
                medmnist_result = self._classify_with_medmnist(image)
                if medmnist_result:
                    return medmnist_result

            # Enhanced heuristic classification
            return self._classify_with_enhanced_heuristics(image, file_name)

        except Exception as e:
            logger.warning(f"Error in medical image classification: {e}")
            return self._classify_with_enhanced_heuristics(image, file_name)

    def _classify_with_medmnist(self, image: Image.Image) -> Optional[str]:
        """Classify medical image using MedMNIST models."""
        try:
            import medmnist
            import torch
            import torchvision.transforms as transforms

            # Convert image to tensor for MedMNIST
            transform = transforms.Compose([
                transforms.Resize((28, 28)),  # MedMNIST standard size
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.5], std=[0.5])
            ])

            # Convert to grayscale if needed
            if image.mode != 'L':
                image = image.convert('L')

            # This is a placeholder for MedMNIST classification
            # In practice, you would load a pre-trained MedMNIST model
            # and use it for classification
            logger.info("MedMNIST classification attempted (placeholder)")
            return None

        except Exception as e:
            logger.warning(f"MedMNIST classification failed: {e}")
            return None

    def _classify_with_enhanced_heuristics(self, image: Image.Image, file_name: str) -> str:
        """Enhanced heuristic classification for medical images."""
        width, height = image.size
        mode = image.mode
        is_grayscale = mode in ['L', 'LA', '1']
        aspect_ratio = width / height

        # File name analysis
        file_name_lower = file_name.lower()

        # Check for specific medical image indicators in filename
        if any(term in file_name_lower for term in ['xray', 'x-ray', 'chest', 'cxr']):
            return 'chest_xray'
        elif any(term in file_name_lower for term in ['ct', 'computed_tomography']):
            return 'computed_tomography'
        elif any(term in file_name_lower for term in ['mri', 'magnetic_resonance']):
            return 'magnetic_resonance'
        elif any(term in file_name_lower for term in ['ultrasound', 'us', 'echo']):
            return 'ultrasound'
        elif any(term in file_name_lower for term in ['mammo', 'mammography']):
            return 'mammography'
        elif any(term in file_name_lower for term in ['endoscopy', 'endoscopic']):
            return 'endoscopy'
        elif any(term in file_name_lower for term in ['dermato', 'skin', 'dermatology']):
            return 'dermatological_image'
        elif any(term in file_name_lower for term in ['retina', 'fundus', 'ophthalmology']):
            return 'retinal_image'
        elif any(term in file_name_lower for term in ['pathology', 'histology', 'microscopy']):
            return 'pathological_image'

        # Enhanced image characteristic analysis
        if is_grayscale:
            # Grayscale medical images
            if 0.9 <= aspect_ratio <= 1.1:  # Square-ish
                if width >= 512 and height >= 512:
                    return 'radiological_scan'  # Likely CT or MRI
                else:
                    return 'medical_radiograph'
            elif aspect_ratio > 1.2:  # Landscape
                if width > 1000:
                    return 'chest_xray'
                else:
                    return 'medical_radiograph'
            else:  # Portrait
                return 'medical_radiograph'
        else:
            # Color medical images
            if width > 1500 or height > 1500:
                return 'high_resolution_clinical_image'
            elif 0.8 <= aspect_ratio <= 1.2:  # Square-ish color images
                if width >= 512:
                    return 'clinical_photograph'
                else:
                    return 'microscopy_image'
            else:
                return 'clinical_photograph'

    def _extract_medical_context(self, image: Image.Image, file_name: str, medical_type: str) -> Dict[str, Any]:
        """Extract additional medical context from image analysis."""
        context = {
            'image_characteristics': self._analyze_image_characteristics(image),
            'filename_indicators': self._extract_filename_indicators(file_name),
            'medical_relevance_score': self._calculate_medical_relevance_score(image, file_name, medical_type)
        }

        return context

    def _analyze_image_characteristics(self, image: Image.Image) -> Dict[str, Any]:
        """Analyze detailed image characteristics relevant to medical imaging."""
        try:
            # Convert to numpy array for analysis
            img_array = np.array(image)

            characteristics = {
                'mean_intensity': float(np.mean(img_array)),
                'std_intensity': float(np.std(img_array)),
                'min_intensity': float(np.min(img_array)),
                'max_intensity': float(np.max(img_array)),
                'dynamic_range': float(np.max(img_array) - np.min(img_array)),
            }

            # Calculate contrast metrics
            if len(img_array.shape) == 2:  # Grayscale
                characteristics['contrast_ratio'] = float(np.std(img_array) / np.mean(img_array)) if np.mean(img_array) > 0 else 0

            return characteristics

        except Exception as e:
            logger.warning(f"Error analyzing image characteristics: {e}")
            return {}

    def _extract_filename_indicators(self, file_name: str) -> List[str]:
        """Extract medical indicators from filename."""
        indicators = []
        file_name_lower = file_name.lower()

        medical_terms = [
            'xray', 'x-ray', 'chest', 'cxr', 'ct', 'mri', 'ultrasound', 'us',
            'mammo', 'mammography', 'endoscopy', 'dermato', 'retina', 'fundus',
            'pathology', 'histology', 'microscopy', 'radiograph', 'scan'
        ]

        for term in medical_terms:
            if term in file_name_lower:
                indicators.append(term)

        return indicators

    def _calculate_medical_relevance_score(self, image: Image.Image, file_name: str, medical_type: str) -> float:
        """Calculate a relevance score for medical context."""
        score = 0.5  # Base score

        # Filename indicators
        filename_indicators = self._extract_filename_indicators(file_name)
        score += len(filename_indicators) * 0.1

        # Image characteristics
        if image.mode in ['L', 'LA', '1']:  # Grayscale often indicates medical imaging
            score += 0.2

        # Size considerations
        width, height = image.size
        if width >= 512 and height >= 512:  # Medical images often high resolution
            score += 0.1

        # Medical type specificity
        if medical_type != 'medical_image':  # More specific than generic
            score += 0.2

        return min(1.0, score)  # Cap at 1.0

    def _create_fallback_analysis(self, file_bytes: bytes, file_name: str, error_msg: str) -> Dict[str, Any]:
        """Create fallback analysis when other methods fail."""
        return {
            'is_dicom': False,
            'medical_type': 'medical_image',
            'file_size_bytes': len(file_bytes),
            'analysis_error': error_msg,
            'fallback_analysis': True,
            'medical_context': {
                'filename_indicators': self._extract_filename_indicators(file_name),
                'medical_relevance_score': 0.3  # Low confidence fallback
            }
        }

    def create_medical_description(self, file_name: str, analysis: Dict[str, Any]) -> str:
        """
        Create comprehensive medical image description for embedding.

        Args:
            file_name: Name of the image file
            analysis: Analysis results from analyze_medical_image

        Returns:
            Comprehensive medical description string
        """
        description_parts = [f"Medical image: {file_name}"]

        # Add medical type
        medical_type = analysis.get('medical_type', 'medical_image')
        description_parts.append(f"Type: {medical_type.replace('_', ' ')}")

        # Add DICOM-specific information
        if analysis.get('is_dicom', False):
            modality = analysis.get('modality', 'Unknown')
            description_parts.append(f"DICOM modality: {modality}")

            body_part = analysis.get('body_part_examined', '')
            if body_part and body_part != 'Unknown':
                description_parts.append(f"Body part: {body_part}")

            study_desc = analysis.get('study_description', '')
            if study_desc:
                description_parts.append(f"Study: {study_desc}")

            series_desc = analysis.get('series_description', '')
            if series_desc:
                description_parts.append(f"Series: {series_desc}")

        # Add image characteristics
        if 'width' in analysis and 'height' in analysis:
            description_parts.append(f"Dimensions: {analysis['width']}x{analysis['height']}")

        # Add medical context
        if analysis.get('is_grayscale', False):
            description_parts.append("Grayscale medical image")
        elif not analysis.get('is_dicom', False):
            description_parts.append("Color clinical image")

        # Add filename indicators
        medical_context = analysis.get('medical_context', {})
        filename_indicators = medical_context.get('filename_indicators', [])
        if filename_indicators:
            description_parts.append(f"Medical indicators: {', '.join(filename_indicators)}")

        # Add relevance information
        relevance_score = medical_context.get('medical_relevance_score', 0)
        if relevance_score > 0.7:
            description_parts.append("High medical relevance")
        elif relevance_score > 0.5:
            description_parts.append("Moderate medical relevance")

        return ". ".join(description_parts) + "."
