#!/usr/bin/env python3
"""
Test script for the enhanced medical image classification system.
"""

import os
import sys
import io
from PIL import Image
import numpy as np

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.medical_image_classifier import MedicalImageClassifier
from utils.document_processor import DocumentProcessor

def create_test_image(image_type="chest_xray"):
    """Create a test medical image for testing."""
    if image_type == "chest_xray":
        # Create a grayscale landscape image (typical chest X-ray)
        width, height = 1200, 800
        image = Image.new('L', (width, height), color=128)
        
        # Add some simple patterns to simulate chest X-ray
        pixels = np.array(image)
        # Add some darker areas (lungs)
        pixels[200:600, 300:500] = 80
        pixels[200:600, 700:900] = 80
        # Add brighter area (heart)
        pixels[300:500, 550:650] = 180
        
        image = Image.fromarray(pixels)
    elif image_type == "ct_scan":
        # Create a square grayscale image (typical CT scan)
        width, height = 512, 512
        image = Image.new('L', (width, height), color=100)
        
        # Add circular pattern
        pixels = np.array(image)
        center_x, center_y = width // 2, height // 2
        y, x = np.ogrid[:height, :width]
        mask = (x - center_x)**2 + (y - center_y)**2 < (width//3)**2
        pixels[mask] = 150
        
        image = Image.fromarray(pixels)
    elif image_type == "clinical_photo":
        # Create a color image (clinical photograph)
        width, height = 800, 600
        image = Image.new('RGB', (width, height), color=(200, 180, 160))
        
        # Add some color variation
        pixels = np.array(image)
        pixels[100:500, 200:600] = [220, 200, 180]
        
        image = Image.fromarray(pixels)
    else:
        # Default medical image
        width, height = 600, 600
        image = Image.new('L', (width, height), color=120)
    
    return image

def test_medical_image_classifier():
    """Test the medical image classifier."""
    print("=== Testing Medical Image Classifier ===")
    
    classifier = MedicalImageClassifier()
    
    # Test different types of medical images
    test_cases = [
        ("chest_xray_test.jpg", "chest_xray"),
        ("ct_scan_test.jpg", "ct_scan"),
        ("clinical_photo_test.jpg", "clinical_photo"),
        ("mri_brain_scan.jpg", "ct_scan"),  # Use CT scan image for MRI test
    ]
    
    for filename, image_type in test_cases:
        print(f"\n--- Testing {filename} ({image_type}) ---")
        
        # Create test image
        test_image = create_test_image(image_type)
        
        # Convert to bytes
        img_bytes = io.BytesIO()
        test_image.save(img_bytes, format='JPEG')
        image_bytes = img_bytes.getvalue()
        
        # Analyze the image
        analysis = classifier.analyze_medical_image(image_bytes, filename)
        
        print(f"Analysis results:")
        print(f"  Medical type: {analysis.get('medical_type', 'unknown')}")
        print(f"  Is DICOM: {analysis.get('is_dicom', False)}")
        print(f"  Dimensions: {analysis.get('width', 'unknown')}x{analysis.get('height', 'unknown')}")
        print(f"  Is grayscale: {analysis.get('is_grayscale', 'unknown')}")
        
        if 'medical_context' in analysis:
            context = analysis['medical_context']
            print(f"  Medical relevance score: {context.get('medical_relevance_score', 'unknown')}")
            print(f"  Filename indicators: {context.get('filename_indicators', [])}")
        
        # Create medical description
        description = classifier.create_medical_description(filename, analysis)
        print(f"  Medical description: {description[:100]}...")

def test_document_processor():
    """Test the document processor with enhanced medical image classification."""
    print("\n=== Testing Document Processor ===")
    
    processor = DocumentProcessor()
    
    # Create a test medical image
    test_image = create_test_image("chest_xray")
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    test_image.save(img_bytes, format='JPEG')
    image_bytes = img_bytes.getvalue()
    
    # Process the image
    chunks = processor._process_image_bytes(image_bytes, "test_chest_xray.jpg")
    
    print(f"Generated {len(chunks)} chunks")
    if chunks:
        chunk = chunks[0]
        print(f"Content: {chunk['content'][:100]}...")
        print(f"Metadata keys: {list(chunk['metadata'].keys())}")
        
        image_info = chunk['metadata'].get('image_info', {})
        print(f"Medical type: {image_info.get('medical_type', 'unknown')}")
        print(f"Is DICOM: {image_info.get('is_dicom', False)}")

def main():
    """Main test function."""
    print("Enhanced Medical Image Classification System Test")
    print("=" * 50)
    
    try:
        # Test the medical image classifier
        test_medical_image_classifier()
        
        # Test the document processor integration
        test_document_processor()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        print("\nKey improvements implemented:")
        print("- DICOM metadata extraction support")
        print("- Enhanced filename-based medical image classification")
        print("- Comprehensive medical context generation")
        print("- Integration with BiomedVLP models")
        print("- Fallback mechanisms for non-DICOM images")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
