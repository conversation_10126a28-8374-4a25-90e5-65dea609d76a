version: '3.8'

services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: health-chatbot-qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant-data:/qdrant/storage
    networks:
      - health-chatbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: health-chatbot-backend
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - PORT=5000
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev-jwt-key-change-in-production}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEV_JWT_KEY=${DEV_JWT_KEY:-dev-jwt-key}
      - DEV_OPENAI_KEY=${DEV_OPENAI_KEY}
      - PROD_JWT_KEY=${PROD_JWT_KEY}
      - PROD_OPENAI_KEY=${PROD_OPENAI_KEY}
      - VECTOR_DB_URL=http://qdrant:6333
      - VECTOR_DB_COLLECTION=health_documents
    ports:
      - "5000:5000"
    volumes:
      - ./backend/data:/app/data:ro
      - backend-uploads:/app/uploads
    depends_on:
      qdrant:
        condition: service_healthy
    networks:
      - health-chatbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: health-chatbot-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - health-chatbot-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  health-chatbot-network:
    driver: bridge

volumes:
  backend-data:
    driver: local
  backend-uploads:
    driver: local
  qdrant-data:
    driver: local
