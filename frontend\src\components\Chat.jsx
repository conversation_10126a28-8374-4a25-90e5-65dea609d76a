import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { chatService } from '../services/api';
import ReactMarkdown from 'react-markdown';
import './Chat.css';

const Chat = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Auto-resize textarea based on content
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset to default height
      textarea.style.height = '40px';

      // Calculate the scroll height and add a small buffer
      const newHeight = Math.min(textarea.scrollHeight, 150);

      // Only update if height needs to change
      if (newHeight > 40) {
        textarea.style.height = `${newHeight}px`;
      }
    }
  }, [input]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle key press events
  const handleKeyDown = (e) => {
    // Submit on Enter without Shift key
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!input.trim() || loading) return;

    const userMessage = {
      text: input,
      sender: 'user',
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    try {
      const response = await chatService.sendMessage(input.trim());

      const botMessage = {
        text: response.answer,
        sender: 'bot',
        timestamp: new Date().toISOString(),
        sources: response.sources || []
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Error sending message:', error);

      const errorMessage = {
        text: 'Sorry, there was an error processing your request. Please try again.',
        sender: 'bot',
        isError: true,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="chat-container">
      <div className="chat-header">
        <h2>Health Chatbot</h2>
        <div className="user-role">
          Logged in as: <span>{user?.role || 'User'}</span>
        </div>
      </div>

      <div className="messages-container">
        {messages.length === 0 ? (
          <div className="welcome-message">
            <h3>Welcome to the Health Chatbot!</h3>
            <p>
              {user?.role === 'professional'
                ? 'Ask any medical questions to get detailed clinical insights.'
                : 'Ask any health-related questions for easy-to-understand answers.'}
            </p>
          </div>
        ) : (
          messages.map((msg, index) => (
            <div
              key={index}
              className={`message ${msg.sender} ${msg.isError ? 'error' : ''}`}
            >
              <div className="message-content">
                {msg.sender === 'bot' ? (
                  <>
                    <ReactMarkdown>{msg.text}</ReactMarkdown>
                    {msg.sources && msg.sources.length > 0 && (
                      <div className="message-sources">
                        <div className="sources-header">Sources:</div>
                        <ul className="sources-list">
                          {msg.sources.map((source, idx) => (
                            <li key={idx} className="source-item">
                              <span className="source-name">{source.filename || source.source}</span>
                              {source.page && <span className="source-page">Page {source.page}</span>}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </>
                ) : (
                  msg.text
                )}
              </div>
              <div className="message-timestamp">
                {new Date(msg.timestamp).toLocaleTimeString()}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      <form className="chat-input-form" onSubmit={handleSubmit}>
        <textarea
          ref={textareaRef}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type your health question here..."
          disabled={loading}
          rows="1"
          className="chat-textarea"
        />
        <button type="submit" disabled={loading || !input.trim()}>
          {loading ? 'Sending...' : 'Send'}
        </button>
      </form>
    </div>
  );
};

export default Chat;
