.document-upload {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.document-upload h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #2c3e50;
}

.upload-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.file-input-container {
  position: relative;
}

.file-input {
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  z-index: -1;
}

.file-label {
  display: block;
  background-color: #e9ecef;
  color: #495057;
  padding: 10px 15px;
  border-radius: 4px;
  border: 1px solid #ced4da;
  cursor: pointer;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  transition: background-color 0.2s;
}

.file-label:hover {
  background-color: #dee2e6;
}

.progress-container {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.error-message {
  color: #dc3545;
  font-size: 0.9rem;
}

.upload-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.upload-button:hover:not(:disabled) {
  background-color: #0069d9;
}

.upload-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.upload-info {
  margin-top: 15px;
  font-size: 0.85rem;
  color: #6c757d;
}

.upload-info p {
  margin: 5px 0;
}
