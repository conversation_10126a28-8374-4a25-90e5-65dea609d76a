.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #2c3e50;
  color: white;
}

.logo a {
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
  text-decoration: none;
}

.nav {
  display: flex;
  align-items: center;
}

.nav a {
  color: white;
  margin-right: 1.5rem;
  text-decoration: none;
  transition: all 0.2s;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
}

.nav a:hover {
  opacity: 0.8;
}

.nav a.active {
  background-color: #3498db;
  color: white;
  opacity: 1;
}

.logout-button {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.main-content {
  flex: 1;
}

.footer {
  padding: 1rem 2rem;
  background-color: #f5f8fa;
  border-top: 1px solid #e2e8f0;
  text-align: center;
  color: #718096;
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer p {
  margin: 0.25rem 0;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}
