.messages-container {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.welcome-message {
  text-align: center;
  margin: auto 0;
  padding: var(--spacing-xl);
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

.welcome-message h3 {
  margin-top: 0;
  color: var(--color-secondary);
}

.welcome-message p {
  color: var(--color-text-light);
  line-height: 1.5;
}

.message {
  max-width: 80%;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-md);
  border-radius: var(--border-radius-md);
  position: relative;
}

.message.user {
  align-self: flex-end;
  background-color: var(--color-primary);
  color: var(--color-white);
  border-bottom-right-radius: 0;
}

.message.bot {
  align-self: flex-start;
  background-color: var(--color-white);
  color: var(--color-text);
  border-bottom-left-radius: 0;
  box-shadow: var(--shadow-sm);
}

.message.error {
  background-color: var(--color-error-bg);
  color: var(--color-error);
}

.message-content {
  word-break: break-word;
  line-height: 1.5;
}

/* Markdown styling */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.message-content h1 {
  font-size: 1.5rem;
}

.message-content h2 {
  font-size: 1.3rem;
}

.message-content h3 {
  font-size: 1.1rem;
}

.message-content p {
  margin-bottom: var(--spacing-sm);
}

.message-content ul,
.message-content ol {
  margin-left: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

.message-content li {
  margin-bottom: var(--spacing-xs);
}

.message-content a {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

.message-content code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2rem 0.4rem;
  border-radius: var(--border-radius-sm);
  font-family: monospace;
}

.message-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  overflow-x: auto;
  margin-bottom: var(--spacing-sm);
}

.message-content blockquote {
  border-left: 3px solid var(--color-border);
  padding-left: var(--spacing-md);
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
}

/* Adjust styles for bot messages */
.message.bot .message-content {
  color: var(--color-text);
}

.message.bot .message-content a {
  color: var(--color-primary);
}

/* Adjust styles for user messages */
.message.user .message-content {
  color: var(--color-white);
}

.message.user .message-content a {
  color: var(--color-white);
  text-decoration: underline;
}

.message-timestamp {
  font-size: var(--font-size-xs);
  opacity: 0.7;
  margin-top: var(--spacing-xs);
  text-align: right;
}
